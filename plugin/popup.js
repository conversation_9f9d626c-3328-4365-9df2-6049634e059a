// 临时邮箱服务插件主要逻辑
class TempEmailService {
    constructor() {
        this.apiBaseUrl = 'https://1.12.224.176/api';
        this.apiKey = 'temp-email-service-2024'; // 硬编码API密钥
        this.domain = 'shengchai.dpdns.org';
        this.currentEmail = null;
        this.userId = null;
        this.emailCheckInterval = null;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadLocalRules();
        this.generateUserId();
    }

    // 生成用户ID（时间戳 + 随机数）
    generateUserId() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 8);
        this.userId = `${timestamp}_${random}`;
    }

    // 生成随机邮箱地址
    generateRandomEmail() {
        const length = Math.floor(Math.random() * 5) + 8; // 8-12位
        const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return `${result}@${this.domain}`;
    }

    // 绑定事件
    bindEvents() {
        document.getElementById('generateBtn').addEventListener('click', () => this.generateEmail());
        document.getElementById('refreshBtn').addEventListener('click', () => this.refreshEmails());
        document.getElementById('copyBtn').addEventListener('click', () => this.copyToClipboard(this.currentEmail));
        document.getElementById('copyCodeBtn').addEventListener('click', () => this.copyVerificationCode());
        document.getElementById('copyLinkBtn').addEventListener('click', () => this.copyVerificationLink());
        document.getElementById('toggleRuleForm').addEventListener('click', () => this.toggleRuleForm());
        document.getElementById('saveLocalBtn').addEventListener('click', () => this.saveLocalRule());
        document.getElementById('shareBtn').addEventListener('click', () => this.shareRule());
    }

    // 生成新的临时邮箱
    async generateEmail() {
        try {
            this.updateStatus('正在生成临时邮箱...', 'loading');
            
            this.currentEmail = this.generateRandomEmail();
            const generateTime = new Date().toISOString();
            
            // 更新界面
            document.getElementById('emailAddress').textContent = this.currentEmail;
            document.getElementById('copyBtn').style.display = 'inline-block';
            document.getElementById('refreshBtn').style.display = 'inline-block';
            
            // 发送到服务器
            const response = await fetch(`${this.apiBaseUrl}/generate-email`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': this.apiKey
                },
                body: JSON.stringify({
                    email: this.currentEmail,
                    generateTime: generateTime,
                    userId: this.userId
                })
            });

            if (response.ok) {
                this.updateStatus('临时邮箱生成成功！等待邮件...', 'success');
                this.startEmailPolling();
            } else {
                throw new Error('服务器响应错误');
            }
        } catch (error) {
            this.updateStatus('生成失败：' + error.message, 'error');
            console.error('生成邮箱失败:', error);
        }
    }

    // 开始轮询邮件
    startEmailPolling() {
        if (this.emailCheckInterval) {
            clearInterval(this.emailCheckInterval);
        }
        
        // 立即检查一次
        this.refreshEmails();
        
        // 每5秒检查一次
        this.emailCheckInterval = setInterval(() => {
            this.refreshEmails();
        }, 5000);
    }

    // 刷新邮件
    async refreshEmails() {
        if (!this.currentEmail || !this.userId) return;
        
        try {
            const response = await fetch(`${this.apiBaseUrl}/get-emails?userId=${this.userId}`, {
                headers: {
                    'X-API-Key': this.apiKey
                }
            });

            if (response.ok) {
                const emails = await response.json();
                this.displayEmails(emails);
                if (emails.length > 0) {
                    this.parseEmails(emails);
                }
            }
        } catch (error) {
            console.error('获取邮件失败:', error);
        }
    }

    // 显示邮件列表
    displayEmails(emails) {
        const emailList = document.getElementById('emailList');
        
        if (emails.length === 0) {
            emailList.innerHTML = '<div class="no-emails">暂无邮件</div>';
            return;
        }

        emailList.innerHTML = emails.map(email => `
            <div class="email-item">
                <div class="email-header">
                    <span class="sender">${email.from}</span>
                    <span class="time">${new Date(email.date).toLocaleString()}</span>
                </div>
                <div class="email-subject">${email.subject}</div>
                <div class="email-content">${email.content}</div>
            </div>
        `).join('');
    }

    // 解析邮件内容
    async parseEmails(emails) {
        for (const email of emails) {
            const rule = await this.findParsingRule(email.from);
            if (rule) {
                const parsed = this.applyParsingRule(email.content, rule);
                if (parsed.code || parsed.link) {
                    this.displayParseResults(parsed);
                    break; // 找到第一个匹配的就停止
                }
            }
        }
    }

    // 查找解析规则
    async findParsingRule(senderEmail) {
        // 先查找本地规则
        const localRules = await this.getLocalRules();
        const localRule = localRules.find(rule => rule.sender === senderEmail);
        if (localRule) return localRule;

        // 再查找共享规则
        try {
            const response = await fetch(`${this.apiBaseUrl}/get-shared-rules`, {
                headers: {
                    'X-API-Key': this.apiKey
                }
            });
            if (response.ok) {
                const sharedRules = await response.json();
                return sharedRules.find(rule => rule.sender === senderEmail);
            }
        } catch (error) {
            console.error('获取共享规则失败:', error);
        }
        
        return null;
    }

    // 应用解析规则
    applyParsingRule(content, rule) {
        const result = { code: null, link: null };
        
        try {
            if (rule.codePattern) {
                const codeMatch = content.match(new RegExp(rule.codePattern));
                if (codeMatch) {
                    result.code = codeMatch[1] || codeMatch[0];
                }
            }
            
            if (rule.linkPattern) {
                const linkMatch = content.match(new RegExp(rule.linkPattern));
                if (linkMatch) {
                    result.link = linkMatch[1] || linkMatch[0];
                }
            }
        } catch (error) {
            console.error('解析规则应用失败:', error);
        }
        
        return result;
    }

    // 显示解析结果
    displayParseResults(parsed) {
        if (parsed.code) {
            document.getElementById('codeValue').textContent = parsed.code;
            document.getElementById('copyCodeBtn').style.display = 'inline-block';
        }
        
        if (parsed.link) {
            const linkElement = document.getElementById('linkValue');
            linkElement.href = parsed.link;
            linkElement.textContent = parsed.link;
            linkElement.style.display = 'inline';
            document.getElementById('copyLinkBtn').style.display = 'inline-block';
        }
    }

    // 更新状态显示
    updateStatus(message, type = 'info') {
        const statusElement = document.getElementById('status');
        statusElement.textContent = message;
        statusElement.className = `status ${type}`;
    }

    // 复制到剪贴板
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.updateStatus('已复制到剪贴板', 'success');
        } catch (error) {
            console.error('复制失败:', error);
            this.updateStatus('复制失败', 'error');
        }
    }

    // 复制验证码
    copyVerificationCode() {
        const code = document.getElementById('codeValue').textContent;
        if (code && code !== '-') {
            this.copyToClipboard(code);
        }
    }

    // 复制验证链接
    copyVerificationLink() {
        const link = document.getElementById('linkValue').href;
        if (link && link !== '#') {
            this.copyToClipboard(link);
        }
    }

    // 切换规则表单显示
    toggleRuleForm() {
        const form = document.getElementById('ruleForm');
        const isVisible = form.style.display !== 'none';
        form.style.display = isVisible ? 'none' : 'block';
        document.getElementById('toggleRuleForm').textContent = isVisible ? '添加新规则' : '取消';
    }

    // 保存本地规则
    async saveLocalRule() {
        const rule = this.collectRuleData();
        if (!rule) return;

        try {
            const localRules = await this.getLocalRules();
            localRules.push(rule);
            await chrome.storage.local.set({ localRules });
            
            this.updateStatus('规则已保存到本地', 'success');
            this.clearRuleForm();
            this.loadLocalRules();
        } catch (error) {
            this.updateStatus('保存失败：' + error.message, 'error');
        }
    }

    // 共享规则
    async shareRule() {
        const rule = this.collectRuleData();
        if (!rule) return;

        try {
            const response = await fetch(`${this.apiBaseUrl}/upload-shared-rule`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': this.apiKey
                },
                body: JSON.stringify(rule)
            });

            if (response.ok) {
                this.updateStatus('规则已共享给所有用户', 'success');
                this.clearRuleForm();
            } else {
                throw new Error('服务器响应错误');
            }
        } catch (error) {
            this.updateStatus('共享失败：' + error.message, 'error');
        }
    }

    // 收集规则数据
    collectRuleData() {
        const sender = document.getElementById('senderEmail').value.trim();
        const sample = document.getElementById('emailSample').value.trim();
        const example = document.getElementById('codeExample').value.trim();

        if (!sender || !sample || !example) {
            this.updateStatus('请填写完整的规则信息', 'error');
            return null;
        }

        // 生成解析模式
        const patterns = this.generateParsingPatterns(sample, example);
        
        return {
            sender,
            sample,
            example,
            codePattern: patterns.codePattern,
            linkPattern: patterns.linkPattern,
            createdAt: new Date().toISOString()
        };
    }

    // 生成解析模式
    generateParsingPatterns(sample, example) {
        // 简单的模式生成逻辑
        const escapedExample = example.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const codePattern = `(${escapedExample})`;
        
        // 查找链接模式
        const linkMatch = sample.match(/(https?:\/\/[^\s]+)/);
        const linkPattern = linkMatch ? '(https?:\\/\\/[^\\s]+)' : null;
        
        return { codePattern, linkPattern };
    }

    // 清空规则表单
    clearRuleForm() {
        document.getElementById('senderEmail').value = '';
        document.getElementById('emailSample').value = '';
        document.getElementById('codeExample').value = '';
        this.toggleRuleForm();
    }

    // 获取本地规则
    async getLocalRules() {
        try {
            const result = await chrome.storage.local.get(['localRules']);
            return result.localRules || [];
        } catch (error) {
            console.error('获取本地规则失败:', error);
            return [];
        }
    }

    // 加载本地规则显示
    async loadLocalRules() {
        const rules = await this.getLocalRules();
        const container = document.getElementById('localRules');
        
        if (rules.length === 0) {
            container.innerHTML = '暂无本地规则';
            return;
        }

        container.innerHTML = rules.map((rule, index) => `
            <div class="rule-item">
                <div class="rule-sender">${rule.sender}</div>
                <div class="rule-example">示例：${rule.example}</div>
                <button class="delete-rule-btn" data-index="${index}">删除</button>
            </div>
        `).join('');

        // 绑定删除事件
        container.querySelectorAll('.delete-rule-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.deleteLocalRule(parseInt(e.target.dataset.index));
            });
        });
    }

    // 删除本地规则
    async deleteLocalRule(index) {
        try {
            const rules = await this.getLocalRules();
            rules.splice(index, 1);
            await chrome.storage.local.set({ localRules: rules });
            this.loadLocalRules();
            this.updateStatus('规则已删除', 'success');
        } catch (error) {
            this.updateStatus('删除失败：' + error.message, 'error');
        }
    }
}

    // 页面卸载时清理
    cleanup() {
        if (this.emailCheckInterval) {
            clearInterval(this.emailCheckInterval);
        }
    }
}

// 初始化服务
document.addEventListener('DOMContentLoaded', () => {
    const service = new TempEmailService();

    // 页面卸载时清理资源
    window.addEventListener('beforeunload', () => {
        service.cleanup();
    });
});
